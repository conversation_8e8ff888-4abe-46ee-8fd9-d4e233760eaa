// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["driverAdapters"]
  output          = "../generated/client"
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model User {
  id      String  @id @default(cuid())
  clerkId String  @unique
  email   String  @unique
  name    String?
  picture String?

  // Extension connection

  extensionApiKey   String?
  sessionToken      String? // For webapp session authentication
  lastExtensionSync DateTime?
  lastSettingsSync  DateTime?
  extensionEnabled  Boolean   @default(true)
  lastActiveAt      DateTime?
  termsAccepted     <PERSON>olean   @default(false)
  termsAcceptedAt   DateTime?

  // Subscription (sync with extension)
  subscriptionTier   String @default("FREE")
  subscriptionStatus String @default("ACTIVE")

  // Cubent Unit tracking
  cubentUnitsUsed   Float     @default(0)
  cubentUnitsLimit  Float     @default(50)
  unitsResetDate    DateTime?
  // Settings sync
  extensionSettings Json?
  preferences       Json?

  // Relations
  extensionSessions     ExtensionSession[]
  usageMetrics          UsageMetrics[]
  apiKeys               ApiKey[]
  usageAnalytics        UsageAnalytics[]
  autocompleteAnalytics AutocompleteAnalytics[]
  autocompleteMetrics   AutocompleteMetrics[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ExtensionSession {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  sessionId    String
  isActive     Boolean  @default(true)
  lastActiveAt DateTime @default(now())

  // Extension details
  extensionVersion String?
  vscodeVersion    String?
  platform         String?
  metadata         Json?

  // Usage tracking
  tokensUsed   Int @default(0)
  requestsMade Int @default(0)

  createdAt DateTime @default(now())

  @@unique([userId, sessionId])
  @@index([userId])
  @@index([isActive])
}

model UsageMetrics {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  tokensUsed      Int   @default(0)
  inputTokens     Int   @default(0)
  outputTokens    Int   @default(0)
  cacheReadTokens Int   @default(0)
  cacheWriteTokens Int  @default(0)
  cubentUnitsUsed Float @default(0)
  requestsMade    Int   @default(0)
  costAccrued     Float @default(0)

  date DateTime @default(now())

  @@index([userId])
  @@index([date])
}

model UsageAnalytics {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  modelId         String
  tokensUsed      Int     @default(0)
  inputTokens     Int     @default(0)
  outputTokens    Int     @default(0)
  cacheReadTokens Int     @default(0)
  cacheWriteTokens Int    @default(0)
  cubentUnitsUsed Float   @default(0)
  requestsMade    Int     @default(0)
  costAccrued     Float   @default(0)
  sessionId       String?
  metadata        Json?

  createdAt DateTime @default(now())

  @@index([userId])
  @@index([modelId])
  @@index([createdAt])
}

model AutocompleteAnalytics {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Autocomplete specific fields
  modelId              String   // e.g., "codestral", "mercury-coder", "qwen-coder"
  provider             String   // e.g., "mistral", "inception-labs", "ollama"
  completionsGenerated Int      @default(0) // Number of completions generated
  completionsAccepted  Int      @default(0) // Number of completions accepted by user
  linesAdded           Int      @default(0) // Total lines of code added through autocomplete
  charactersAdded      Int      @default(0) // Total characters added through autocomplete

  // Context and metadata
  language             String?  // Programming language (e.g., "typescript", "python")
  filepath             String?  // File path where completion occurred
  sessionId            String?  // Session identifier

  // Performance metrics
  avgLatency           Float?   @default(0) // Average completion latency in ms
  successRate          Float?   @default(0) // Success rate (completions generated / requests)
  acceptanceRate       Float?   @default(0) // Acceptance rate (accepted / generated)

  // Additional metadata
  metadata             Json?    // Additional context (file type, project info, etc.)

  createdAt DateTime @default(now())

  @@index([userId])
  @@index([modelId])
  @@index([provider])
  @@index([language])
  @@index([createdAt])
}

model AutocompleteMetrics {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Daily aggregated autocomplete metrics
  completionsGenerated Int   @default(0) // Total completions generated this day
  completionsAccepted  Int   @default(0) // Total completions accepted this day
  linesAdded           Int   @default(0) // Total lines added this day
  charactersAdded      Int   @default(0) // Total characters added this day

  // Performance aggregates
  avgLatency           Float @default(0) // Average latency for the day
  avgSuccessRate       Float @default(0) // Average success rate for the day
  avgAcceptanceRate    Float @default(0) // Average acceptance rate for the day

  // Model breakdown (JSON object with model usage stats)
  modelBreakdown       Json? // { "codestral": { completions: 50, accepted: 30 }, ... }

  date DateTime @default(now())

  @@index([userId])
  @@index([date])
}

model ApiKey {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  name        String
  description String?
  keyHash     String    @unique
  permissions Json      @default("[]")
  isActive    Boolean   @default(true)
  expiresAt   DateTime?
  lastUsedAt  DateTime?
  usageCount  Int       @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([isActive])
}

model UserProfile {
  id     String @id @default(cuid())
  userId String @unique

  email String
  name  String?

  // Extension settings
  subscriptionTier   String  @default("FREE")
  subscriptionStatus String  @default("ACTIVE")
  termsAccepted      Boolean @default(false)
  extensionEnabled   Boolean @default(true)
  settings           Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

model PendingLogin {
  id        String   @id @default(cuid())
  deviceId  String
  state     String
  token     String
  userId    String // Store the user ID for token validation
  createdAt DateTime @default(now())
  expiresAt DateTime

  @@index([deviceId])
  @@index([state])
  @@index([expiresAt])
  @@index([token])
}

// Keep the existing Page model for now
model Page {
  id   Int    @id @default(autoincrement())
  name String
}
